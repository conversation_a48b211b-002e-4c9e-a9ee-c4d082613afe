/**
 * Medical Device Management System
 * Enhanced Custom CSS Styles
 */

/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

/* General Styles */
body {
    font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
    font-weight: 400;
    line-height: 1.6;
}

.rtl {
    direction: rtl;
    text-align: right;
}

.ltr {
    direction: ltr;
    text-align: left;
}

/* Enhanced Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 1rem;
}

.text-gradient {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Enhanced Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

.slide-in-right {
    animation: slideInRight 0.6s ease-out;
}

/* Loading Spinner */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Enhanced Sidebar */
.sidebar {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    z-index: 100;
    padding: 0;
    background: linear-gradient(180deg, #2c3e50 0%, #34495e 100%);
    box-shadow: 4px 0 20px rgba(0, 0, 0, 0.1);
    border-right: 1px solid rgba(255, 255, 255, 0.1);
}

.rtl .sidebar {
    right: 0;
    left: auto;
    box-shadow: -4px 0 20px rgba(0, 0, 0, 0.1);
    border-right: none;
    border-left: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-sticky {
    position: relative;
    top: 0;
    height: 100vh;
    padding-top: 1rem;
    overflow-x: hidden;
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: rgba(255, 255, 255, 0.3) transparent;
}

.sidebar-sticky::-webkit-scrollbar {
    width: 6px;
}

.sidebar-sticky::-webkit-scrollbar-track {
    background: transparent;
}

.sidebar-sticky::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
}

.sidebar .nav-link {
    font-weight: 500;
    color: rgba(255, 255, 255, 0.8);
    padding: 0.875rem 1.25rem;
    margin: 0.25rem 0.75rem;
    border-radius: 0.5rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.sidebar .nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s;
}

.sidebar .nav-link:hover::before {
    left: 100%;
}

.sidebar .nav-link:hover {
    color: #fff;
    background: rgba(255, 255, 255, 0.1);
    transform: translateX(5px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.rtl .sidebar .nav-link:hover {
    transform: translateX(-5px);
}

.sidebar .nav-link.active {
    color: #fff;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
    transform: translateX(5px);
}

.rtl .sidebar .nav-link.active {
    transform: translateX(-5px);
}

/* Icon styles removed since icons are no longer used in sidebar */

.sidebar-heading {
    font-size: 0.75rem;
    text-transform: uppercase;
    color: rgba(255, 255, 255, 0.6);
    padding: 1rem 1.25rem 0.5rem;
    font-weight: 600;
    letter-spacing: 1px;
}

/* Main Content */
.main-content {
    margin-left: 240px;
    padding: 2rem;
}

.rtl .main-content {
    margin-left: 0;
    margin-right: 240px;
}

@media (max-width: 767.98px) {
    .main-content {
        margin-left: 0;
    }
    
    .rtl .main-content {
        margin-right: 0;
    }
    
    .sidebar {
        width: 100%;
        height: auto;
        position: relative;
    }
    
    .sidebar-sticky {
        height: auto;
    }
}

/* Navbar */
.navbar-brand {
    padding-top: .75rem;
    padding-bottom: .75rem;
    font-size: 1rem;
    background-color: rgba(0, 0, 0, .25);
    box-shadow: inset -1px 0 0 rgba(0, 0, 0, .25);
}

.rtl .navbar-brand {
    box-shadow: inset 1px 0 0 rgba(0, 0, 0, .25);
}

.navbar .navbar-toggler {
    top: .25rem;
    right: 1rem;
}

.rtl .navbar .navbar-toggler {
    right: auto;
    left: 1rem;
}

.navbar .form-control {
    padding: .75rem 1rem;
    border-width: 0;
    border-radius: 0;
}

/* Enhanced Cards */
.card {
    border-radius: 1rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
    border: none;
    background: #fff;
    transition: all 0.3s ease;
    overflow: hidden;
    position: relative;
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.card-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    padding: 1.25rem 1.5rem;
    font-weight: 600;
    color: #2c3e50;
}

.card-body {
    padding: 1.5rem;
}

.card-footer {
    background: #f8f9fa;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    padding: 1rem 1.5rem;
}

/* Glass Card Effect */
.card-glass {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Card Variants */
.card-primary {
    border-left: 4px solid #007bff;
}

.card-success {
    border-left: 4px solid #28a745;
}

.card-warning {
    border-left: 4px solid #ffc107;
}

.card-danger {
    border-left: 4px solid #dc3545;
}

.card-info {
    border-left: 4px solid #17a2b8;
}

/* Enhanced Dashboard Stats */
.stats-card {
    border: none;
    border-radius: 1rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    background: #fff;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
}

.stats-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

.stats-card.primary::before {
    background: linear-gradient(90deg, #007bff 0%, #0056b3 100%);
}

.stats-card.success::before {
    background: linear-gradient(90deg, #28a745 0%, #1e7e34 100%);
}

.stats-card.warning::before {
    background: linear-gradient(90deg, #ffc107 0%, #e0a800 100%);
}

.stats-card.danger::before {
    background: linear-gradient(90deg, #dc3545 0%, #c82333 100%);
}

.stats-card .card-body {
    padding: 1.5rem;
    position: relative;
}

.stats-card .stats-icon {
    font-size: 2.5rem;
    opacity: 0.2;
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    transition: all 0.3s ease;
}

.rtl .stats-card .stats-icon {
    right: auto;
    left: 1rem;
}

.stats-card:hover .stats-icon {
    opacity: 0.3;
    transform: translateY(-50%) scale(1.1);
}

.stats-card .stats-number {
    font-size: 2rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 0.25rem;
    line-height: 1;
}

.stats-card .stats-text {
    color: #6c757d;
    font-size: 0.875rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Animated Counter */
.stats-number.animate {
    animation: pulse 2s ease-in-out;
}

/* Enhanced Tables */
.table-responsive {
    border-radius: 1rem;
    overflow: hidden;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    background: #fff;
}

.table {
    margin-bottom: 0;
    font-size: 0.9rem;
}

.table th {
    border-top: none;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    font-weight: 600;
    color: #2c3e50;
    padding: 1rem 0.75rem;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border-bottom: 2px solid #dee2e6;
}

.table td {
    padding: 0.875rem 0.75rem;
    vertical-align: middle;
    border-bottom: 1px solid #f1f3f4;
}

.table-hover tbody tr {
    transition: all 0.3s ease;
}

.table-hover tbody tr:hover {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
    transform: scale(1.01);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

/* Table Actions */
.table .btn-group {
    display: flex;
    gap: 0.25rem;
}

.table .btn-sm {
    padding: 0.375rem 0.5rem;
    font-size: 0.8rem;
    border-radius: 0.5rem;
    transition: all 0.3s ease;
}

.table .btn-sm:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* DataTables Enhancements */
.dataTables_wrapper {
    padding: 1.5rem;
}

.dataTables_filter input {
    border-radius: 0.5rem;
    border: 1px solid #dee2e6;
    padding: 0.5rem 0.75rem;
    margin-left: 0.5rem;
}

.dataTables_length select {
    border-radius: 0.5rem;
    border: 1px solid #dee2e6;
    padding: 0.375rem 0.75rem;
    margin: 0 0.5rem;
}

.dataTables_paginate .paginate_button {
    border-radius: 0.5rem !important;
    margin: 0 0.125rem;
    transition: all 0.3s ease;
}

.dataTables_paginate .paginate_button:hover {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    border-color: transparent !important;
    color: white !important;
}

/* Status Badges */
.badge-operational {
    background-color: #28a745;
    color: #fff;
}

.badge-under-maintenance {
    background-color: #ffc107;
    color: #212529;
}

.badge-out-of-order {
    background-color: #dc3545;
    color: #fff;
}

.badge-retired {
    background-color: #6c757d;
    color: #fff;
}

.badge-scheduled {
    background-color: #17a2b8;
    color: #fff;
}

.badge-completed {
    background-color: #28a745;
    color: #fff;
}

.badge-overdue {
    background-color: #dc3545;
    color: #fff;
}

.badge-cancelled {
    background-color: #6c757d;
    color: #fff;
}

.badge-open {
    background-color: #17a2b8;
    color: #fff;
}

.badge-in-progress {
    background-color: #ffc107;
    color: #212529;
}

.badge-resolved {
    background-color: #28a745;
    color: #fff;
}

.badge-closed {
    background-color: #6c757d;
    color: #fff;
}

.badge-low {
    background-color: #28a745;
    color: #fff;
}

.badge-medium {
    background-color: #ffc107;
    color: #212529;
}

.badge-high {
    background-color: #fd7e14;
    color: #fff;
}

.badge-critical {
    background-color: #dc3545;
    color: #fff;
}

/* Enhanced Forms */
.form-control {
    border-radius: 0.75rem;
    border: 2px solid #e9ecef;
    padding: 0.75rem 1rem;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    background: #fff;
}

.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    transform: translateY(-2px);
}

.form-control:hover {
    border-color: #ced4da;
}

.form-select {
    border-radius: 0.75rem;
    border: 2px solid #e9ecef;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.form-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.form-text {
    color: #6c757d;
    font-size: 0.8rem;
    margin-top: 0.25rem;
}

/* Input Groups */
.input-group {
    border-radius: 0.75rem;
    overflow: hidden;
}

.input-group .form-control {
    border-radius: 0;
}

.input-group .form-control:first-child {
    border-top-left-radius: 0.75rem;
    border-bottom-left-radius: 0.75rem;
}

.input-group .form-control:last-child {
    border-top-right-radius: 0.75rem;
    border-bottom-right-radius: 0.75rem;
}

.input-group-text {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 2px solid #e9ecef;
    color: #6c757d;
    font-weight: 500;
}

/* Form Validation */
.was-validated .form-control:valid,
.form-control.is-valid {
    border-color: #28a745;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

.was-validated .form-control:invalid,
.form-control.is-invalid {
    border-color: #dc3545;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

.valid-feedback {
    color: #28a745;
    font-size: 0.8rem;
    font-weight: 500;
}

.invalid-feedback {
    color: #dc3545;
    font-size: 0.8rem;
    font-weight: 500;
}

/* Enhanced Buttons */
.btn {
    border-radius: 0.5rem;
    padding: 0.75rem 1.5rem;
    font-weight: 500;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    position: relative;
    overflow: hidden;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 120px;
    white-space: nowrap;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.btn:active {
    transform: translateY(0);
}

.btn-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
    border-color: #007bff;
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
}

.btn-primary:hover {
    background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
    border-color: #0056b3;
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.4);
}

.btn-success {
    background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
    color: white;
    border-color: #28a745;
    box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
}

.btn-success:hover {
    background: linear-gradient(135deg, #1e7e34 0%, #155724 100%);
    border-color: #1e7e34;
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.4);
}

.btn-secondary {
    background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
    color: white;
    border-color: #6c757d;
    box-shadow: 0 2px 8px rgba(108, 117, 125, 0.3);
}

.btn-secondary:hover {
    background: linear-gradient(135deg, #495057 0%, #343a40 100%);
    border-color: #495057;
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(108, 117, 125, 0.4);
}

.btn-outline-primary {
    color: #007bff;
    border-color: #007bff;
    background: transparent;
}

.btn-outline-primary:hover {
    background: #007bff;
    border-color: #007bff;
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
}

.btn-outline-secondary {
    color: #6c757d;
    border-color: #6c757d;
    background: transparent;
}

.btn-outline-secondary:hover {
    background: #6c757d;
    border-color: #6c757d;
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(108, 117, 125, 0.3);
}

.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
    min-width: 80px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.btn-lg {
    padding: 1rem 2rem;
    font-size: 1rem;
    min-width: 150px;
}

.btn-warning {
    background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
    box-shadow: 0 4px 15px rgba(255, 193, 7, 0.4);
    color: #212529;
}

.btn-warning:hover {
    background: linear-gradient(135deg, #e0a800 0%, #e8650e 100%);
    box-shadow: 0 8px 25px rgba(255, 193, 7, 0.6);
    color: #212529;
}

.btn-danger {
    background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%);
    box-shadow: 0 4px 15px rgba(220, 53, 69, 0.4);
}

.btn-danger:hover {
    background: linear-gradient(135deg, #c82333 0%, #d91a72 100%);
    box-shadow: 0 8px 25px rgba(220, 53, 69, 0.6);
}

.btn-outline-primary {
    border: 2px solid #667eea;
    color: #667eea;
    background: transparent;
}

.btn-outline-primary:hover {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-color: #667eea;
    color: white;
}

.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
    border-radius: 0.5rem;
}

.btn-lg {
    padding: 1rem 2rem;
    font-size: 1rem;
    border-radius: 1rem;
}

.btn-icon {
    padding: 0.5rem;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

.btn-icon i {
    font-size: 1rem;
}

/* Button Groups */
.btn-group .btn {
    border-radius: 0;
    margin-right: 2px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.3px;
}

.btn-group .btn:first-child {
    border-top-left-radius: 0.75rem;
    border-bottom-left-radius: 0.75rem;
}

.btn-group .btn:last-child {
    border-top-right-radius: 0.75rem;
    border-bottom-right-radius: 0.75rem;
    margin-right: 0;
}

/* Enhanced Action Buttons */
.btn-group .btn-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    border-color: #007bff;
    color: white;
    min-width: 70px;
}

.btn-group .btn-warning {
    background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
    border-color: #ffc107;
    color: #212529;
    min-width: 60px;
}

.btn-group .btn-danger {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    border-color: #dc3545;
    color: white;
    min-width: 70px;
}

.btn-group .btn-info {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
    border-color: #17a2b8;
    color: white;
    min-width: 80px;
}

/* Floating Action Button */
.btn-float {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    z-index: 1000;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.rtl .btn-float {
    right: auto;
    left: 2rem;
}

/* QR Code */
.qr-code-container {
    text-align: center;
    margin: 1rem 0;
}

.qr-code-image {
    max-width: 200px;
    border: 1px solid #dee2e6;
    padding: 0.5rem;
    background-color: #fff;
}

/* Notifications */
.notification-badge {
    position: absolute;
    top: 5px;
    right: 5px;
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    line-height: 1;
    border-radius: 10rem;
}

.rtl .notification-badge {
    right: auto;
    left: 5px;
}

.notification-item {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid #dee2e6;
}

.notification-item:last-child {
    border-bottom: none;
}

.notification-item.unread {
    background-color: rgba(0, 123, 255, 0.05);
}

.notification-title {
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.notification-time {
    font-size: 0.75rem;
    color: #6c757d;
}

/* Enhanced Login Page */
.login-bg {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    z-index: -1;
}

.login-bg-shapes {
    position: absolute;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.shape {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    animation: float 6s ease-in-out infinite;
}

.shape-1 {
    width: 80px;
    height: 80px;
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.shape-2 {
    width: 120px;
    height: 120px;
    top: 60%;
    right: 15%;
    animation-delay: 2s;
}

.shape-3 {
    width: 60px;
    height: 60px;
    bottom: 20%;
    left: 20%;
    animation-delay: 4s;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-20px);
    }
}

.login-card {
    border-radius: 1.5rem;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.login-logo {
    text-align: center;
    margin-bottom: 2rem;
}

.logo-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
}

.logo-icon i {
    font-size: 2rem;
    color: white;
}

.login-card .form-control {
    border-radius: 0.75rem;
    border: 2px solid #e9ecef;
    padding: 0.875rem 1rem;
    transition: all 0.3s ease;
}

.login-card .form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    transform: translateY(-2px);
}

.login-card .input-group-text {
    border-radius: 0.75rem 0 0 0.75rem;
    border: 2px solid #e9ecef;
    border-right: none;
    background: #f8f9fa;
}

.login-card .btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 0.75rem;
    padding: 0.875rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
}

.login-card .btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.4);
}

/* Language Toggle */
.language-toggle .btn {
    border-radius: 2rem;
    padding: 0.5rem 1.5rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.language-toggle .btn:hover {
    transform: translateY(-2px);
}

/* Footer */
.footer {
    padding: 1.5rem 0;
    color: #6c757d;
    border-top: 1px solid #dee2e6;
    margin-top: 3rem;
}

/* Enhanced Modals */
.modal-content {
    border-radius: 1rem;
    border: none;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
}

.modal-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 1rem 1rem 0 0;
    padding: 1.5rem;
}

.modal-title {
    font-weight: 600;
    color: #2c3e50;
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 0 0 1rem 1rem;
    padding: 1.5rem;
    background: #f8f9fa;
}

/* Enhanced Alerts */
.alert {
    border-radius: 1rem;
    border: none;
    padding: 1rem 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.alert-primary {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    color: #667eea;
    border-left: 4px solid #667eea;
}

.alert-success {
    background: linear-gradient(135deg, rgba(40, 167, 69, 0.1) 0%, rgba(32, 201, 151, 0.1) 100%);
    color: #28a745;
    border-left: 4px solid #28a745;
}

.alert-warning {
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.1) 0%, rgba(253, 126, 20, 0.1) 100%);
    color: #856404;
    border-left: 4px solid #ffc107;
}

.alert-danger {
    background: linear-gradient(135deg, rgba(220, 53, 69, 0.1) 0%, rgba(232, 62, 140, 0.1) 100%);
    color: #dc3545;
    border-left: 4px solid #dc3545;
}

.alert-info {
    background: linear-gradient(135deg, rgba(23, 162, 184, 0.1) 0%, rgba(102, 126, 234, 0.1) 100%);
    color: #17a2b8;
    border-left: 4px solid #17a2b8;
}

/* Enhanced Dropdowns */
.dropdown-menu {
    border-radius: 0.75rem;
    border: none;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
    padding: 0.5rem 0;
    margin-top: 0.5rem;
}

.dropdown-item {
    padding: 0.75rem 1.5rem;
    transition: all 0.3s ease;
    border-radius: 0;
}

.dropdown-item:hover {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    transform: translateX(5px);
}

.dropdown-item.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

/* Enhanced Pagination */
.pagination {
    gap: 0.25rem;
}

.page-link {
    border-radius: 0.5rem;
    border: 2px solid #e9ecef;
    color: #667eea;
    padding: 0.5rem 0.75rem;
    transition: all 0.3s ease;
    margin: 0 0.125rem;
}

.page-link:hover {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-color: #667eea;
    color: white;
    transform: translateY(-2px);
}

.page-item.active .page-link {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-color: #667eea;
    color: white;
}

/* Enhanced Progress Bars */
.progress {
    height: 0.75rem;
    border-radius: 0.5rem;
    background: #e9ecef;
    overflow: hidden;
}

.progress-bar {
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    transition: width 0.6s ease;
}

/* Enhanced Tooltips */
.tooltip .tooltip-inner {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    border-radius: 0.5rem;
    padding: 0.5rem 0.75rem;
    font-size: 0.8rem;
    font-weight: 500;
}

/* Responsive Enhancements */
@media (max-width: 768px) {
    .stats-card .stats-icon {
        font-size: 2rem;
        position: static;
        margin-bottom: 0.5rem;
        opacity: 0.3;
    }

    .card-body {
        padding: 1rem;
    }

    .btn {
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
    }

    .table-responsive {
        font-size: 0.8rem;
    }
}

/* Print Styles */
@media print {
    .sidebar, .navbar, .no-print, .btn, .dropdown {
        display: none !important;
    }

    .main-content {
        margin-left: 0 !important;
        margin-right: 0 !important;
        padding: 0 !important;
    }

    .card {
        box-shadow: none !important;
        border: 1px solid #dee2e6 !important;
        break-inside: avoid;
    }

    body {
        background: #fff !important;
        color: #000 !important;
    }

    .text-gradient {
        color: #2c3e50 !important;
        background: none !important;
        -webkit-text-fill-color: initial !important;
    }
}
