<?php
/**
 * Users List View
 * 
 * This file displays the list of users.
 */

// Set page title
$pageTitle = __('users');

// Start output buffering
ob_start();
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 text-gradient mb-1"><?php echo __('users_management'); ?></h1>
        <p class="text-muted mb-0"><?php echo __('manage_system_users'); ?></p>
    </div>

    <div class="d-flex gap-2">
        <?php if (hasPermission('manage_users')): ?>
        <a href="<?php echo getBaseUrl(); ?>/users/create" class="btn btn-primary">
            <?php echo __('add_user'); ?>
        </a>
        <?php endif; ?>

        <div class="dropdown">
            <button class="btn btn-outline-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                <i class="fas fa-download me-2"></i><?php echo __('export'); ?>
            </button>
            <ul class="dropdown-menu">
                <li><a class="dropdown-item" href="<?php echo getBaseUrl(); ?>/users/export?format=pdf">
                    <i class="fas fa-file-pdf me-2 text-danger"></i>PDF
                </a></li>
                <li><a class="dropdown-item" href="<?php echo getBaseUrl(); ?>/users/export?format=excel">
                    <i class="fas fa-file-excel me-2 text-success"></i>Excel
                </a></li>
                <li><a class="dropdown-item" href="<?php echo getBaseUrl(); ?>/users/export?format=csv">
                    <i class="fas fa-file-csv me-2 text-info"></i>CSV
                </a></li>
            </ul>
        </div>
    </div>
</div>

<!-- Enhanced Filters -->
<div class="card card-glass mb-4 fade-in-up">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-filter me-2"></i><?php echo __('filters'); ?>
        </h5>
    </div>
    <div class="card-body">
        <form method="GET" action="<?php echo getBaseUrl(); ?>/users" class="row g-3">
            <div class="col-md-3">
                <label for="hospital_id" class="form-label"><?php echo __('hospital'); ?></label>
                <select class="form-select" id="hospital_id" name="hospital_id">
                    <option value=""><?php echo __('all_hospitals'); ?></option>
                    <?php foreach ($hospitals as $hospital): ?>
                        <option value="<?php echo $hospital['id']; ?>" <?php echo ($hospitalId == $hospital['id']) ? 'selected' : ''; ?>>
                            <?php echo htmlspecialchars($hospital['name']); ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            
            <div class="col-md-3">
                <label for="role" class="form-label"><?php echo __('role'); ?></label>
                <select class="form-select" id="role" name="role">
                    <option value=""><?php echo __('all_roles'); ?></option>
                    <option value="admin" <?php echo ($role == 'admin') ? 'selected' : ''; ?>><?php echo __('admin'); ?></option>
                    <option value="manager" <?php echo ($role == 'manager') ? 'selected' : ''; ?>><?php echo __('manager'); ?></option>
                    <option value="technician" <?php echo ($role == 'technician') ? 'selected' : ''; ?>><?php echo __('technician'); ?></option>
                    <option value="user" <?php echo ($role == 'user') ? 'selected' : ''; ?>><?php echo __('user'); ?></option>
                </select>
            </div>
            
            <div class="col-md-3">
                <label for="status" class="form-label"><?php echo __('status'); ?></label>
                <select class="form-select" id="status" name="status">
                    <option value=""><?php echo __('all_statuses'); ?></option>
                    <option value="active" <?php echo ($status == 'active') ? 'selected' : ''; ?>><?php echo __('active'); ?></option>
                    <option value="inactive" <?php echo ($status == 'inactive') ? 'selected' : ''; ?>><?php echo __('inactive'); ?></option>
                </select>
            </div>
            
            <div class="col-md-3">
                <label for="search" class="form-label"><?php echo __('search'); ?></label>
                <div class="input-group">
                    <input type="text" class="form-control" id="search" name="search" value="<?php echo htmlspecialchars($search ?? ''); ?>" placeholder="<?php echo __('search_users'); ?>">
                    <button class="btn btn-primary" type="submit">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>

            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <button type="button" class="btn btn-outline-secondary btn-sm" onclick="clearFilters()">
                        <i class="fas fa-times me-1"></i><?php echo __('clear_filters'); ?>
                    </button>
                    <small class="text-muted">
                        <?php echo count($users); ?> <?php echo __('users_found'); ?>
                    </small>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Enhanced Users Table -->
<div class="card fade-in-up" style="animation-delay: 0.2s;">
    <div class="card-header">
        <div class="d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">
                <i class="fas fa-list me-2"></i><?php echo __('users_list'); ?>
            </h5>
        </div>
    </div>
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-hover mb-0" id="users-table">
                <thead>
                    <tr>
                        <th><?php echo __('name'); ?></th>
                        <th><?php echo __('username'); ?></th>
                        <th><?php echo __('email'); ?></th>
                        <th><?php echo __('role'); ?></th>
                        <th><?php echo __('hospital'); ?></th>
                        <th><?php echo __('status'); ?></th>
                        <th><?php echo __('last_login'); ?></th>
                        <th><?php echo __('actions'); ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (empty($users)): ?>
                        <tr>
                            <td colspan="8" class="text-center"><?php echo __('no_users'); ?></td>
                        </tr>
                    <?php else: ?>
                        <?php foreach ($users as $user): ?>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-sm me-2">
                                            <?php if (!empty($user['avatar'])): ?>
                                                <img src="<?php echo getBaseUrl(); ?>/<?php echo $user['avatar']; ?>" alt="Avatar" class="rounded-circle" width="32" height="32">
                                            <?php else: ?>
                                                <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 32px; height: 32px;">
                                                    <?php echo strtoupper(substr($user['full_name'], 0, 1)); ?>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                        <div>
                                            <?php echo htmlspecialchars($user['full_name']); ?>
                                        </div>
                                    </div>
                                </td>
                                <td><?php echo htmlspecialchars($user['username']); ?></td>
                                <td><?php echo htmlspecialchars($user['email']); ?></td>
                                <td>
                                    <span class="badge bg-<?php echo getRoleColor($user['role']); ?>">
                                        <?php echo __($user['role']); ?>
                                    </span>
                                </td>
                                <td>
                                    <?php if ($user['hospital_name']): ?>
                                        <?php echo htmlspecialchars($user['hospital_name']); ?>
                                    <?php else: ?>
                                        <span class="text-muted"><?php echo __('no_hospital'); ?></span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <span class="badge bg-<?php echo $user['status'] === 'active' ? 'success' : 'secondary'; ?>">
                                        <?php echo __($user['status']); ?>
                                    </span>
                                </td>
                                <td>
                                    <?php if ($user['last_login']): ?>
                                        <?php 
                                        $lastLogin = new DateTime($user['last_login']);
                                        echo $lastLogin->format('Y-m-d H:i');
                                        ?>
                                    <?php else: ?>
                                        <span class="text-muted"><?php echo __('never'); ?></span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="btn-group">
                                        <a href="<?php echo getBaseUrl(); ?>/users/view/<?php echo $user['id']; ?>" class="btn btn-sm btn-outline-primary" title="<?php echo __('view'); ?>">
                                            <?php echo __('view'); ?>
                                        </a>

                                        <?php if (hasPermission('manage_users')): ?>
                                        <a href="<?php echo getBaseUrl(); ?>/users/edit/<?php echo $user['id']; ?>" class="btn btn-sm btn-outline-secondary" title="<?php echo __('edit'); ?>">
                                            <?php echo __('edit'); ?>
                                        </a>

                                        <?php if ($user['id'] != $_SESSION['user']['id']): ?>
                                        <button type="button" class="btn btn-sm btn-outline-warning" onclick="toggleStatus(<?php echo $user['id']; ?>, '<?php echo $user['status']; ?>')" title="<?php echo $user['status'] === 'active' ? __('deactivate') : __('activate'); ?>">
                                            <?php echo $user['status'] === 'active' ? __('deactivate') : __('activate'); ?>
                                        </button>
                                        
                                        <button type="button" class="btn btn-sm btn-outline-danger" onclick="confirmDelete(<?php echo $user['id']; ?>, '<?php echo htmlspecialchars($user['full_name']); ?>')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                        <?php endif; ?>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><?php echo __('confirm_delete'); ?></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p><?php echo __('delete_user_confirm'); ?></p>
                <p><strong id="userName"></strong></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><?php echo __('cancel'); ?></button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                    <button type="submit" class="btn btn-danger"><?php echo __('delete'); ?></button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Status Toggle Modal -->
<div class="modal fade" id="statusModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><?php echo __('confirm_status_change'); ?></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p id="statusMessage"></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><?php echo __('cancel'); ?></button>
                <form id="statusForm" method="POST" style="display: inline;">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                    <input type="hidden" name="action" value="toggle_status">
                    <button type="submit" class="btn btn-warning"><?php echo __('confirm'); ?></button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function confirmDelete(userId, userName) {
    document.getElementById('userName').textContent = userName;
    document.getElementById('deleteForm').action = '<?php echo getBaseUrl(); ?>/users/delete/' + userId;
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}

function toggleStatus(userId, currentStatus) {
    const newStatus = currentStatus === 'active' ? 'inactive' : 'active';
    const action = currentStatus === 'active' ? '<?php echo __('deactivate'); ?>' : '<?php echo __('activate'); ?>';
    
    document.getElementById('statusMessage').textContent = '<?php echo __('confirm_status_change_message'); ?>'.replace('%action%', action);
    document.getElementById('statusForm').action = '<?php echo getBaseUrl(); ?>/users/toggle_status/' + userId;
    new bootstrap.Modal(document.getElementById('statusModal')).show();
}

function clearFilters() {
    window.location.href = '<?php echo getBaseUrl(); ?>/users';
}

// Auto-submit form when filters change
document.getElementById('hospital_id').addEventListener('change', function() {
    this.form.submit();
});

document.getElementById('role').addEventListener('change', function() {
    this.form.submit();
});

document.getElementById('status').addEventListener('change', function() {
    this.form.submit();
});
</script>

<?php
// Get the content
$content = ob_get_clean();

// Include the layout
include 'views/layout.php';
?>
